"""
Logging Configuration for Bybit Trading Bot
Provides structured logging with file rotation and console output
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from typing import Optional


def setup_logging(
    log_level: str = "INFO",
    log_file: Optional[str] = None,
    console_output: bool = True,
    max_bytes: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5
) -> logging.Logger:
    """
    Set up comprehensive logging for the trading bot
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Path to log file (optional)
        console_output: Whether to output to console
        max_bytes: Maximum file size before rotation
        backup_count: Number of backup files to keep
        
    Returns:
        Configured logger instance
    """
    
    # Create main logger
    logger = logging.getLogger("bybit_trading_bot")
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear any existing handlers
    logger.handlers.clear()
    
    # Create formatter with more detailed information
    formatter = logging.Formatter(
        fmt='%(asctime)s | %(levelname)-8s | %(name)-20s | %(funcName)-15s | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Console handler
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    # File handler with rotation
    if log_file:
        # Ensure log directory exists
        log_dir = os.path.dirname(log_file)
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    # Add error file handler for critical issues
    if log_file:
        error_log_file = log_file.replace('.log', '_errors.log')
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        logger.addHandler(error_handler)
    
    # Trade activity logger (separate file for trade logs)
    trade_logger = logging.getLogger("bybit_trading_bot.trades")
    trade_logger.setLevel(logging.INFO)
    
    if log_file:
        trade_log_file = log_file.replace('.log', '_trades.log')
        trade_handler = logging.handlers.RotatingFileHandler(
            trade_log_file,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        trade_formatter = logging.Formatter(
            fmt='%(asctime)s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        trade_handler.setFormatter(trade_formatter)
        trade_logger.addHandler(trade_handler)
    
    # Hardware monitoring logger
    hardware_logger = logging.getLogger("bybit_trading_bot.hardware")
    hardware_logger.setLevel(logging.INFO)
    
    if log_file:
        hardware_log_file = log_file.replace('.log', '_hardware.log')
        hardware_handler = logging.handlers.RotatingFileHandler(
            hardware_log_file,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        hardware_handler.setFormatter(formatter)
        hardware_logger.addHandler(hardware_handler)
    
    # Performance logger
    performance_logger = logging.getLogger("bybit_trading_bot.performance")
    performance_logger.setLevel(logging.INFO)
    
    if log_file:
        performance_log_file = log_file.replace('.log', '_performance.log')
        performance_handler = logging.handlers.RotatingFileHandler(
            performance_log_file,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        performance_handler.setFormatter(formatter)
        performance_logger.addHandler(performance_handler)
    
    logger.info("🔧 Logging system initialized")
    logger.info(f"📝 Log level: {log_level}")
    if log_file:
        logger.info(f"📁 Log file: {log_file}")
    
    return logger


class TradingBotLogger:
    """
    Enhanced logger class with trading-specific methods
    """
    
    def __init__(self, config=None):
        self.main_logger = logging.getLogger("bybit_trading_bot")
        self.trade_logger = logging.getLogger("bybit_trading_bot.trades")
        self.hardware_logger = logging.getLogger("bybit_trading_bot.hardware")
        self.performance_logger = logging.getLogger("bybit_trading_bot.performance")
        
        if config:
            setup_logging(
                log_level=config.log_level,
                log_file=config.log_file,
                console_output=True
            )
    
    def info(self, message: str):
        """Log info message"""
        self.main_logger.info(message)
    
    def warning(self, message: str):
        """Log warning message"""
        self.main_logger.warning(message)
    
    def error(self, message: str):
        """Log error message"""
        self.main_logger.error(message)
    
    def debug(self, message: str):
        """Log debug message"""
        self.main_logger.debug(message)
    
    def critical(self, message: str):
        """Log critical message"""
        self.main_logger.critical(message)
    
    def log_trade(self, action: str, symbol: str, quantity: float, price: float, 
                  order_id: str = None, profit_loss: float = None):
        """Log trading activity"""
        message = f"TRADE | {action} | {symbol} | Qty: {quantity} | Price: {price}"
        if order_id:
            message += f" | OrderID: {order_id}"
        if profit_loss is not None:
            message += f" | P&L: {profit_loss:.2f}"
        
        self.trade_logger.info(message)
    
    def log_hardware(self, component: str, status: str, value: float = None, unit: str = ""):
        """Log hardware status"""
        message = f"HARDWARE | {component} | {status}"
        if value is not None:
            message += f" | {value}{unit}"
        
        self.hardware_logger.info(message)
    
    def log_performance(self, metric: str, value: float, period: str = ""):
        """Log performance metrics"""
        message = f"PERFORMANCE | {metric} | {value}"
        if period:
            message += f" | Period: {period}"
        
        self.performance_logger.info(message)
    
    def log_strategy_signal(self, strategy: str, symbol: str, signal: str, 
                           confidence: float = None, indicators: dict = None):
        """Log strategy signals"""
        message = f"SIGNAL | {strategy} | {symbol} | {signal}"
        if confidence is not None:
            message += f" | Confidence: {confidence:.2f}"
        if indicators:
            indicator_str = " | ".join([f"{k}: {v}" for k, v in indicators.items()])
            message += f" | {indicator_str}"
        
        self.main_logger.info(message)
    
    def log_risk_event(self, event_type: str, description: str, severity: str = "INFO"):
        """Log risk management events"""
        message = f"RISK | {event_type} | {description}"
        
        if severity.upper() == "ERROR":
            self.main_logger.error(message)
        elif severity.upper() == "WARNING":
            self.main_logger.warning(message)
        else:
            self.main_logger.info(message)
    
    def log_system_event(self, event: str, details: str = ""):
        """Log system events"""
        message = f"SYSTEM | {event}"
        if details:
            message += f" | {details}"
        
        self.main_logger.info(message)
